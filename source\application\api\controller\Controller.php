<?php

namespace app\api\controller;

use app\api\model\User as UserModel;
use app\api\model\Wxapp as WxappModel;
use app\common\exception\BaseException;
use app\common\model\UploadFile;

/**
 * API控制器基类
 * Class BaseController
 * @package app\store\controller
 */
class Controller extends \think\Controller
{
    const JSON_SUCCESS_STATUS = 1;
    const JSON_ERROR_STATUS = 0;

    /* @ver $wxapp_id 小程序id */
    protected $wxapp_id;

    /**
     * API基类初始化
     * @throws BaseException
     * @throws \think\exception\DbException
     */
    public function _initialize()
    {
        // 当前小程序id
        
        if(is_numeric($this->request->param('wxapp_id'))){
            $this->wxapp_id = $this->getWxappId();
        }else{
            $this->wxapp_id = $this->getExWxappId();
        }
        // 验证当前小程序状态
        $this->checkWxapp();
        
    }

    /**
     * 获取当前小程序ID
     * @return mixed
     * @throws BaseException
     */
    private function getWxappId()
    {
        if (!$wxapp_id = $this->request->param('wxapp_id')) {
            throw new BaseException(['msg' => '缺少必要的参数：wxapp_id']);
        }
        return $wxapp_id;
    }
    
    
    private function getExWxappId()
    {
        $wxapp_id_en = str_replace(' ','+',$this->request->param('wxapp_id'));
        if (!$wxapp_id = encrypt($wxapp_id_en,'D')){
             throw new BaseException(['msg' => '无效wxapp_id']);   
        }
        return $wxapp_id;
    }

    /**
     * 验证当前小程序状态
     * @throws BaseException
     * @throws \think\exception\DbException
     */
    private function checkWxapp()
    {
        $wxapp = WxappModel::detail($this->wxapp_id);
        if (empty($wxapp)) {
            throw new BaseException(['msg' => '当前小程序信息不存在']);
        }
        if ($wxapp['is_recycle'] || $wxapp['is_delete']) {
            throw new BaseException(['msg' => '当前小程序已删除']);
        }
    }

    public function withImageById($data,$field,$name=null){
       $image = $name?$name:'image';
   
       foreach ($data as $k => $v){
              if ($v[$field]){
                  $res = UploadFile::getFileName($v[$field]);
                  if ($res)
                      $data[$k][$image] = $res;
              }  
        }
        return $data;
    }
    
    /**
     * 检查是否为开发环境
     * @return bool
     */
    private function isDevelopmentEnv()
    {
        // 检查是否为zalo mini app studio开发环境
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        
        // 检查开发环境的特征
        return (
            strpos($userAgent, 'ZaloStudio') !== false ||
            strpos($referer, 'localhost') !== false ||
            strpos($referer, '127.0.0.1') !== false ||
            strpos($referer, 'mini-app-studio') !== false ||
            $this->request->param('debug_mode') == '1' ||
            config('app_debug') === true
        );
    }
    
    /**
     * 获取当前用户信息
     * @param bool $is_force
     * @return UserModel|bool|null
     * @throws BaseException
     * @throws \think\exception\DbException
     */
    protected function getUser($is_force = true)
    {
        if (!$token = $this->request->param('token')) {
            // 在开发环境中，如果没有token，尝试使用测试token
            if ($this->isDevelopmentEnv() && !$is_force) {
                return $this->createDevUser();
            }
            $is_force && $this->throwError('缺少必要的参数：token', -1);
            return false;
        }
        
        $user = UserModel::getUser($token);
        
        // 如果在开发环境中token验证失败，尝试宽松处理
        if (!$user && $this->isDevelopmentEnv()) {
            // 写入调试日志
            file_put_contents('debug_token.log', date('Y-m-d H:i:s') . " - Token验证失败，开发环境宽松处理: " . $token . "\n", FILE_APPEND);
            
            // 在开发环境中，如果token验证失败，返回一个测试用户而不是抛出错误
            if (!$is_force) {
                return $this->createDevUser();
            }
            
            // 修改错误消息，提示这是开发环境
            $this->throwError('开发环境：token验证失败，请检查zalo认证状态', -2);
        }
        
        if (!$user) {
            $is_force && $this->throwError('没有找到用户信息', -1);
            return false;
        }
        return $user;
    }

    /**
     * 创建开发环境测试用户
     * @return array
     */
    private function createDevUser()
    {
        return [
            'user_id' => 999999,
            'nickName' => 'ZaloStudio测试用户',
            'avatarUrl' => '',
            'mobile' => '1234567890',
            'balance' => 0,
            'open_id' => 'dev_test_user'
        ];
    }

    /**
     * 输出错误信息
     * @param int $code
     * @param $msg
     * @throws BaseException
     */
    protected function throwError($msg, $code = 0)
    {
        throw new BaseException(['code' => $code, 'msg' => $msg]);
    }

    /**
     * 返回封装后的 API 数据到客户端
     * @param int $code
     * @param string $msg
     * @param array $data
     * @return array
     */
    protected function renderJson($code = self::JSON_SUCCESS_STATUS, $msg = '', $data = [])
    {
        return compact('code', 'msg', 'data');
    }

    /**
     * 返回操作成功json
     * @param array $data
     * @param string|array $msg
     * @return array
     */
    protected function renderSuccess($data = [], $msg = 'success')
    {
        return $this->renderJson(self::JSON_SUCCESS_STATUS, $msg, $data);
    }
    
    /**
     * 返回操作成功json
     * @param array $data
     * @param string|array $msg
     * @return array
     */
    protected function renderBannerSuccess($data = [], $length = 0,$msg = 'success')
    {
        return $this->renderBannerJson(self::JSON_SUCCESS_STATUS,$length, $msg,$data);
    }
    
    /**
     * 返回封装后的 API 数据到客户端
     * @param int $code
     * @param string $msg
     * @param array $data
     * @return array
     */
    protected function renderBannerJson($code = self::JSON_SUCCESS_STATUS,$length, $msg = '', $data = [])
    {
        return compact('code', 'length','msg', 'data');
    }
    
    
    /**
     * 返回操作成功json
     * @param array $data
     * @param string|array $msg
     * @return array
     */
    protected function renderSuccessPlus($data = [], $msg = 'success')
    {
        return $this->renderJson(2, $msg, $data);
    }

    /**
     * 返回操作失败json
     * @param string $msg
     * @param array $data
     * @return array
     */
    protected function renderError($msg = 'error', $data = [])
    {
        return $this->renderJson(self::JSON_ERROR_STATUS, $msg, $data);
    }

    /**
     * 获取post数据 (数组)
     * @param $key
     * @return mixed
     */
    protected function postData($key = null)
    {
        return $this->request->post(is_null($key) ? '' : $key . '/a');
    }

}
