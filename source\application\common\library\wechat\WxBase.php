<?php

namespace app\common\library\wechat;

use think\Cache;
use app\common\exception\BaseException;

/**
 * 微信api基类
 * Class wechat
 * @package app\library
 */
class WxBase
{
    protected $appId;
    protected $appSecret;
    protected $appWxappid;
    protected $appwxsecret;
    protected $wx_type;
    protected $error;

    /**
     * 构造函数
     * WxBase constructor.
     * @param $appId
     * @param $appSecret
     */
    public function __construct($appId = null, $appSecret = null,$appWxappid =null,$appwxsecret = null,$wx_type=0)
    {
        $this->setConfig($appId, $appSecret,$appWxappid,$appwxsecret,$wx_type);
    }

    protected function setConfig($appId = null, $appSecret = null,$appWxappid=null,$appwxsecret = null,$wx_type=0)
    {
        !empty($appId) && $this->appId = $appId;
        !empty($appSecret) && $this->appSecret = $appSecret;
        !empty($appWxappid) && $this->appWxappid = $appWxappid;
        !empty($appwxsecret) && $this->appwxsecret = $appwxsecret;
        $this->wx_type = $wx_type;
    }

    /**
     * 写入日志记录
     * @param $values
     * @return bool|int
     */
    protected function doLogs($values)
    {
        return log_write($values);
    }

    /**
     * 获取access_token
     * @return mixed
     * @throws BaseException
     */
    protected function getAccessToken()
    {
        $cacheKey = $this->appId . '@access_token';
        if (!Cache::get($cacheKey)) {
            // 请求API获取 access_token
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
            $result = $this->get($url);
            $response = $this->jsonDecode($result);
            if (array_key_exists('errcode', $response)) {
                throw new BaseException(['msg' => "access_token获取失败，错误信息：{$result}"]);
            }
            // 记录日志
            $this->doLogs([
                'describe' => '获取access_token',
                'url' => $url,
                'appId' => $this->appId,
                'result' => $result
            ]);
            // 写入缓存
            Cache::set($cacheKey, $response['access_token'], 6000);    // 7000
        }
        return Cache::get($cacheKey);
    }
    
        /**
     * 获取access_token
     * @return mixed
     * @throws BaseException
     */
    protected function getStableAccessToken()
    {
        if($this->wx_type==0){
            $cacheKey = $this->appId . '@access_token';
        }else{
            $cacheKey = $this->appWxappid . '@access_token';
        }
        if (!Cache::get($cacheKey)) {
            // 请求API获取 access_token
            $url = "https://api.weixin.qq.com/cgi-bin/stable_token";
            $data = [
                'grant_type'=>'client_credential', 
                'appid' => $this->wx_type==0?$this->appId:$this->appWxappid,
                'secret' =>$this->wx_type==0?$this->appSecret:$this->appwxsecret,
                'force_refresh' => false
            ];
            // $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
            $result = $this->post2($url,$data);
            $response = $this->jsonDecode($result);
            
            if (array_key_exists('errcode', $response)) {
                throw new BaseException(['msg' => "access_token获取失败，错误信息：{$result}"]);
            }
            // 记录日志
            $this->doLogs([
                'describe' => '获取access_token',
                'url' => $url,
                'appId' => $this->wx_type==0?$this->appId:$this->appWxappid,
                'result' => $result
            ]);
            // 写入缓存
            Cache::set($cacheKey, $response['access_token'], 6000);    // 7000
        }
        return Cache::get($cacheKey);
    }
    
    /**
     * 获取access_token
     * @return mixed
     * @throws BaseException
     */
    protected function getAccessTokenForH5()
    {
        $cacheKey = $this->appWxappid . '@access_token';
        if (!Cache::get($cacheKey)) {
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appWxappid}&secret={$this->appwxsecret}";
            $result = $this->get($url);
            $response = $this->jsonDecode($result);
            if (array_key_exists('errcode', $response)) {
                throw new BaseException(['msg' => "access_token获取失败，错误信息：{$result}"]);
            }
            // 记录日志
            $this->doLogs([
                'describe' => '获取access_token',
                'url' => $url,
                'appId' =>$this->appWxappid,
                'result' => $result
            ]);
            // 写入缓存
            Cache::set($cacheKey, $response['access_token'], 6000);    // 7000
        }
        return Cache::get($cacheKey);
    }
    
    /**
     * 获取access_token
     * @return mixed
     * @throws BaseException
     */
    protected function getAccessTokenForWxOpen()
    {
        if($this->wx_type==0){
            $cacheKey = $this->appId . '@access_token';
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appId}&secret={$this->appSecret}";
        }else{
            $cacheKey = $this->appWxappid . '@access_token';
            $url = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$this->appWxappid}&secret={$this->appwxsecret}";
        }
     
        if (!Cache::get($cacheKey)) {
            $result = $this->get($url);
            $response = $this->jsonDecode($result);
            if (array_key_exists('errcode', $response)) {
                throw new BaseException(['msg' => "access_token获取失败，错误信息：{$result}"]);
            }
            // 记录日志
            $this->doLogs([
                'describe' => '获取access_token',
                'url' => $url,
                'appId' => $this->wx_type==0?$this->appId:$this->appWxappid,
                'result' => $result
            ]);
            // 写入缓存
            Cache::set($cacheKey, $response['access_token'], 6000);    // 7000
        }
        return Cache::get($cacheKey);
    }
    

    /**
     * 模拟GET请求 HTTPS的页面
     * @param string $url 请求地址
     * @return string $result
     */
    protected function get($url)
    {
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, 0);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE); // https请求 不验证证书和hosts
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    /**
     * 模拟POST请求
     * @param $url
     * @param array $data
     * @param bool $useCert
     * @param array $sslCert
     * @return mixed
     */
    protected function post($url, $data = [], $useCert = false, $sslCert = [])
    {
        $header = [
            'Content-type: application/json;'
        ];
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($curl, CURLOPT_POST, TRUE);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        if ($useCert == true) {
            // 设置证书：cert 与 key 分别属于两个.pem文件
            curl_setopt($curl, CURLOPT_SSLCERTTYPE, 'PEM');
            curl_setopt($curl, CURLOPT_SSLCERT, $sslCert['certPem']);
            curl_setopt($curl, CURLOPT_SSLKEYTYPE, 'PEM');
            curl_setopt($curl, CURLOPT_SSLKEY, $sslCert['keyPem']);
        }
        $result = curl_exec($curl);
        curl_close($curl);
        return $result;
    }

    /**
     * 模拟POST请求 [第二种方式, 用于兼容微信api]
     * @param $url
     * @param array $data
     * @return mixed
     */
    protected function post2($url, $data = [])
    {
        $header = [
            'Content-Type: application/x-www-form-urlencoded'
        ];
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);//这个是重点。
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }

    /**
     * 数组转json
     * @param $data
     * @return string
     */
    protected function jsonEncode($data)
    {
        return json_encode($data, JSON_UNESCAPED_UNICODE);
    }

    /**
     * json转数组
     * @param $json
     * @return mixed
     */
    protected function jsonDecode($json)
    {
        return json_decode($json, true);
    }

    /**
     * 返回错误信息
     * @return mixed
     */
    public function getError()
    {
        return $this->error;
    }

}
